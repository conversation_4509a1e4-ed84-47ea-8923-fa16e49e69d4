'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Star, Quote } from 'lucide-react';

const testimonials = [
  {
    name: '<PERSON><PERSON>',
    role: 'Satellite Engineer',
    company: 'Kenya Space Agency',
    image: '/api/placeholder/64/64',
    content: '<PERSON> transformed my career. The mentorship program connected me with industry leaders, and the practical courses gave me the skills I needed to land my dream job in satellite engineering.',
    rating: 5,
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    role: 'AI Research Scientist',
    company: 'Ghana Space Science & Technology Institute',
    image: '/api/placeholder/64/64',
    content: 'The AI in Space course was exceptional. The hands-on projects and real-world applications helped me transition from software development to space technology research.',
    rating: 5,
  },
  {
    name: '<PERSON><PERSON>',
    role: 'Cybersecurity Specialist',
    company: 'Egyptian Space Agency',
    image: '/api/placeholder/64/64',
    content: 'As someone new to space technology, Nova\'s beginner-friendly approach and supportive community made all the difference. I\'m now leading cybersecurity initiatives for satellite systems.',
    rating: 5,
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    role: 'Space Entrepreneur',
    company: 'Founder, AstroTech Solutions',
    image: '/api/placeholder/64/64',
    content: 'The business and entrepreneurship courses on Nova helped me launch my space tech startup. The network I built through the platform has been invaluable for partnerships and funding.',
    rating: 5,
  },
  {
    name: 'Aisha Hassan',
    role: 'Data Scientist',
    company: 'Nigerian Space Research Centre',
    image: '/api/placeholder/64/64',
    content: 'Nova\'s data science courses with a focus on space applications opened up a whole new career path for me. The certification helped me stand out in the competitive job market.',
    rating: 5,
  },
  {
    name: 'Jean-Baptiste Uwimana',
    role: 'Robotics Engineer',
    company: 'Rwanda Space Agency',
    image: '/api/placeholder/64/64',
    content: 'The robotics and automation courses were incredibly detailed and practical. I\'ve applied what I learned to develop robotic systems for satellite deployment and maintenance.',
    rating: 5,
  },
];

export function Testimonials() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Success Stories from
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
              {" "}Our Community
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Hear from professionals who have transformed their careers through Nova's comprehensive
            space technology education platform.
          </p>
        </motion.div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="group"
            >
              <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 h-full flex flex-col">
                {/* Quote Icon */}
                <div className="flex justify-between items-start mb-4">
                  <Quote className="h-8 w-8 text-blue-600 opacity-50" />
                  <div className="flex space-x-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                </div>

                {/* Content */}
                <blockquote className="text-gray-700 leading-relaxed mb-6 flex-grow">
                  "{testimonial.content}"
                </blockquote>

                {/* Author */}
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                    {testimonial.name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">
                      {testimonial.name}
                    </div>
                    <div className="text-sm text-gray-600">
                      {testimonial.role}
                    </div>
                    <div className="text-sm text-blue-600">
                      {testimonial.company}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-16"
        >
          <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Write Your Success Story?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Join thousands of professionals who have advanced their careers in space technology.
              Your journey to success starts with a single step.
            </p>
            <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200">
              Start Your Journey Today
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
