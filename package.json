{"name": "nova", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "create-admin": "npx tsx src/scripts/create-admin.ts", "seed": "npx tsx scripts/seed-database.ts"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@next-auth/mongodb-adapter": "^1.1.3", "@tanstack/react-query": "^5.77.2", "@tanstack/react-query-devtools": "^5.77.2", "@types/next-auth": "^3.15.0", "africastalking": "^0.7.3", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "formik": "^2.4.6", "framer-motion": "^12.14.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "mongodb": "^6.16.0", "mongoose": "^8.15.1", "next": "15.3.2", "next-auth": "^4.24.11", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "yup": "^1.6.1", "zod": "^3.25.30"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tsx": "^4.19.2", "typescript": "^5"}}