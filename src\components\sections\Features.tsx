'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  BookOpen,
  Users,
  Briefcase,
  Award,
  Calendar,
  MessageSquare,
  Brain,
  Smartphone
} from 'lucide-react';

const features = [
  {
    icon: BookOpen,
    title: 'Learning Hub',
    description: 'Access comprehensive courses on space technology, satellite engineering, AI, and cybersecurity with interactive content and real-world projects.',
    color: 'bg-blue-100 text-blue-600',
  },
  {
    icon: Users,
    title: 'Mentorship System',
    description: 'Connect with industry experts and experienced professionals for personalized guidance and career development.',
    color: 'bg-green-100 text-green-600',
  },
  {
    icon: Briefcase,
    title: 'Job & Apprenticeship Board',
    description: 'Discover exciting career opportunities in space technology, from internships to senior positions across Africa.',
    color: 'bg-purple-100 text-purple-600',
  },
  {
    icon: Award,
    title: 'Certification Engine',
    description: 'Earn industry-recognized certificates and blockchain-verified credentials to showcase your expertise.',
    color: 'bg-orange-100 text-orange-600',
  },
  {
    icon: Calendar,
    title: 'Events Hub',
    description: 'Join webinars, workshops, job fairs, and networking events to stay connected with the space tech community.',
    color: 'bg-red-100 text-red-600',
  },
  {
    icon: MessageSquare,
    title: 'Community Space',
    description: 'Engage in discussions, share knowledge, and collaborate with fellow space technology enthusiasts.',
    color: 'bg-indigo-100 text-indigo-600',
  },
  {
    icon: Brain,
    title: 'AI-Powered Personalization',
    description: 'Get personalized learning paths, mentor matching, and job recommendations powered by advanced AI.',
    color: 'bg-pink-100 text-pink-600',
  },
  {
    icon: Smartphone,
    title: 'SMS Integration',
    description: 'Stay connected with course reminders, job alerts, and important updates via SMS, ensuring accessibility across Africa.',
    color: 'bg-teal-100 text-teal-600',
  },
];

export function Features() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Everything You Need to
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
              {" "}Succeed in Space Tech
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Nova provides a comprehensive ecosystem for learning, networking, and advancing your career
            in space technology and innovation.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="group"
              >
                <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-gray-200 h-full">
                  {/* Icon */}
                  <div className={`w-12 h-12 rounded-lg ${feature.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="h-6 w-6" />
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Ready to Launch Your Space Tech Journey?
            </h3>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              Join thousands of learners who are already building the future of space technology in Africa.
            </p>
            <button className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200">
              Explore All Features
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
